package com.gofore.aita.tender.data;

import com.gofore.aita.tender.domain.models.Tender;
import com.gofore.aita.tender.domain.models.WorkflowStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

public interface TenderRepository extends MongoRepository<Tender, String>, TenderRepositoryCustom {
  boolean existsByExternalId(String externalId);

    Page<Tender> findAllByWorkflowStatusInAndTitleContainingIgnoreCaseOrWorkflowStatusInAndClientContainingIgnoreCase(WorkflowStatus[] workflowStatuses, String searchString, WorkflowStatus[] workflowStatuses2, String searchString2, Pageable pageable);
    Page<Tender> findAllByWorkflowStatusInAndTitleIsLikeIgnoreCaseOrWorkflowStatusInAndClientIsLikeIgnoreCase(WorkflowStatus[] workflowStatuses, String searchString, WorkflowStatus[] workflowStatuses2, String searchString2, Pageable pageable);
    Page<Tender> findAllByWorkflowStatusInAndTitleIsNearOrWorkflowStatusInAndClientIsNear(WorkflowStatus[] workflowStatuses, String searchString, WorkflowStatus[] workflowStatuses2, String searchString2, Pageable pageable);

    /**
     * Full-text search across multiple fields using MongoDB text index.
     * This method searches for words in any order across title, client, description, and deliveryLocation fields.
     *
     * @param workflowStatuses Array of workflow statuses to filter by
     * @param searchText Text to search for (words can be in any order)
     * @param pageable Pagination and sorting information
     * @return Page of matching tenders
     */
    @Query("{ 'workflowStatus': { $in: ?0 }, $text: { $search: ?1 } }")
    Page<Tender> findByWorkflowStatusInAndTextSearch(WorkflowStatus[] workflowStatuses, String searchText, Pageable pageable);

    /**
     * Full-text search across multiple fields without workflow status filtering.
     * This method searches for words in any order across title, client, description, and deliveryLocation fields.
     *
     * @param searchText Text to search for (words can be in any order)
     * @param pageable Pagination and sorting information
     * @return Page of matching tenders
     */
    @Query("{ $text: { $search: ?0 } }")
    Page<Tender> findByTextSearch(String searchText, Pageable pageable);

}
