package com.gofore.aita.tender.data;

import com.gofore.aita.tender.domain.models.Tender;
import com.gofore.aita.tender.domain.models.WorkflowStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;

public interface TenderRepository extends MongoRepository<Tender, String>, TenderRepositoryCustom {
  boolean existsByExternalId(String externalId);

    Page<Tender> findAllByWorkflowStatusInAndTitleContainingIgnoreCaseOrWorkflowStatusInAndClientContainingIgnoreCase(WorkflowStatus[] workflowStatuses, String searchString, WorkflowStatus[] workflowStatuses2, String searchString2, Pageable pageable);
    Page<Tender> findAllByWorkflowStatusInAndTitleIsLikeIgnoreCaseOrWorkflowStatusInAndClientIsLikeIgnoreCase(WorkflowStatus[] workflowStatuses, String searchString, WorkflowStatus[] workflowStatuses2, String searchString2, Pageable pageable);
    Page<Tender> findAllByWorkflowStatusInAndTitleIsNearOrWorkflowStatusInAndClientIsNear(WorkflowStatus[] workflowStatuses, String searchString, WorkflowStatus[] workflowStatuses2, String searchString2, Pageable pageable);

}
