package com.gofore.aita.tender.domain;

import com.gofore.aita.analysis.api.IAnalysisService;
import com.gofore.aita.analysis.domain.TenderAnalysisService;
import com.gofore.aita.analysis.models.TenderExtractionResult;
import com.gofore.aita.extraction.data.ExtractedDocumentRepository;
import com.gofore.aita.extraction.domain.FileExtractionService;
import com.gofore.aita.extraction.domain.TextProcessingService;
import com.gofore.aita.extraction.models.ExtractedDocument;
import com.gofore.aita.tender.api.util.FileNameHelper;
import com.gofore.aita.tender.data.TenderRepository;
import com.gofore.aita.tender.domain.events.TenderCreated;
import com.gofore.aita.tender.domain.events.TenderUpdated;
import com.gofore.aita.tender.domain.exceptions.AITenderCreationExtractionException;
import com.gofore.aita.tender.domain.exceptions.BadRequestException;
import com.gofore.aita.tender.domain.exceptions.ResourceNotFoundException;
import com.gofore.aita.tender.domain.models.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
public class TenderService {

  private final FileStorageService fileStorageService;
  private final TenderRepository tenderRepository;
  private final ApplicationEventPublisher eventPublisher;
  private final TenderAnalysisService tenderAnalysisService;
  private final IAnalysisService analysisService;
  private final FileExtractionService fileExtractionService;
  private final TextProcessingService textProcessingService;
  private final ExtractedDocumentRepository extractedDocumentRepository;

  public TenderService(
      FileStorageService fileStorageService,
      TenderRepository tenderRepository,
      ApplicationEventPublisher eventPublisher,
      TenderAnalysisService tenderAnalysisService,
      IAnalysisService analysisService,
      FileExtractionService fileExtractionService,
      TextProcessingService textProcessingService,
      ExtractedDocumentRepository extractedDocumentRepository) {
    this.fileStorageService = fileStorageService;
    this.tenderRepository = tenderRepository;
    this.eventPublisher = eventPublisher;
    this.tenderAnalysisService = tenderAnalysisService;
    this.analysisService = analysisService;
    this.fileExtractionService = fileExtractionService;
    this.textProcessingService = textProcessingService;
    this.extractedDocumentRepository = extractedDocumentRepository;
  }

  public Tender save(final Tender tender, List<MultipartFile> files, User user) {
    tender.setCreationTime(OffsetDateTime.now().truncatedTo(ChronoUnit.SECONDS).toString());
    tender.setCreatedBy(user);
    Tender storedTender = tenderRepository.save(tender);

    if (files != null && !files.isEmpty()) {
      storedTender = storeFilesAndExtract(storedTender, files);
    }

    eventPublisher.publishEvent(new TenderCreated(storedTender.getId()));
    return storedTender;
  }

  /**
   * Creates a tender with AI assistance to extract missing fields from description and files.
   *
   * @param tender The partial tender with title, sourceUrl, and description
   * @param files The files to extract information from
   * @param user The user creating the tender
   * @return The created tender with extracted fields
   */
  public Tender createTenderWithAI(final Tender tender, List<MultipartFile> files, User user) {
    log.info("Creating tender with AI assistance: {}", tender.getTitle());

    // save basic tender to get an ID
    tender.setCreationTime(OffsetDateTime.now().truncatedTo(ChronoUnit.SECONDS).toString());
    tender.setCreatedBy(user != null ? user : new User("1", "AITA tender service"));
    Tender storedTender = tenderRepository.save(tender);

    // store & extract files
    if (files != null && !files.isEmpty()) {
      storedTender = storeFilesAndExtract(storedTender, files);
    }

    // combine description and extracted text for AI prompt
    List<ExtractedDocument> extractedDocuments =
        extractedDocumentRepository.findByTenderId(storedTender.getId());
    String description = storedTender.getDescription();

    List<TenderExtractionResult> extractionResults = new ArrayList<>();
    TextProcessingService.TextWithTokenCount textWithTokens =
        textProcessingService.combineTexts(description, extractedDocuments);
    TenderExtractionResult extractionResult = null;

    int tokenCount = textWithTokens.getTokenCount();
    if (tokenCount < 100000) {
      log.info(
          "Token count ({}) does not exceeds 100000, using full text for extraction.", tokenCount);
      extractionResult = analysisService.extractTenderFields(textWithTokens.getText());
    } else {
      log.info(
          "Token count ({}) exceeds 100000, using stepwise extraction for {} documents.",
          tokenCount,
          extractedDocuments.size());

      // extract fields from each file
      for (ExtractedDocument doc : extractedDocuments) {
        TextProcessingService.TextWithTokenCount combinedTextWithTokens =
            textProcessingService.combineTexts(description, List.of(doc));

        TenderExtractionResult extractedTenderFields =
            analysisService.extractTenderFields(combinedTextWithTokens.getText());
        extractionResults.add(extractedTenderFields);
      }
      extractionResult = analysisService.combineTenderFields(extractionResults);
    }

    if (extractionResult == null) {
      // cleanup
      cleanupAfterAIStep(storedTender);
      log.warn(
          "AI extraction returned null – deleted tender and cleaned up files and text extraction.");
      throw new AITenderCreationExtractionException(
          "AI extraction did return null: Please check inputs");
    }

    updateTenderWithExtractedFields(storedTender, extractionResult);

    if (!allRequiredFieldsPresent(storedTender)) {
      cleanupAfterAIStep(storedTender);
      throw new AITenderCreationExtractionException(
          "AI extraction did not fill all mandatory fields: "
              + extractionResult.getAiRawMessage()
              + " please try again with more complete input or try manual creation.");
    }

    // save tender and publish event
    storedTender = tenderRepository.save(storedTender);
    eventPublisher.publishEvent(new TenderCreated(storedTender.getId()));

    log.info("Successfully created tender with AI assistance: {}", storedTender.getId());
    return storedTender;
  }

  private Tender storeFilesAndExtract(Tender tender, List<MultipartFile> files) {
    List<FileMetadata> storedFileMetadataList = new ArrayList<>(files.size());
    try {
      for (MultipartFile file : files) {
        FileMetadata meta =
            fileStorageService.storeFile(
                tender.getId(), file.getOriginalFilename(), file.getInputStream(), file.getSize());
        storedFileMetadataList.add(meta);
      }

      tender.setFiles(storedFileMetadataList);
      tender = tenderRepository.save(tender);

      // trigger extraction (saves ExtractedDocument records)
      fileExtractionService.processFilesForExtraction(tender);
      return tender;
    } catch (Exception e) {
      log.error("Error storing files for tender {}: {}", tender.getId(), e.getMessage(), e);
      // cleanup stored files & tender
      storedFileMetadataList.forEach(fileStorageService::delete);
      tenderRepository.deleteById(tender.getId());
      throw new RuntimeException("Failed to store and extract files", e);
    }
  }

  private void cleanupAfterAIStep(Tender stored) {
    if (stored.getFiles() != null) {
      stored.getFiles().forEach(fileStorageService::delete);
    }
    extractedDocumentRepository.deleteByTenderId(stored.getId());
    tenderRepository.deleteById(stored.getId());
  }

  /**
   * Get a particular Tender
   *
   * @param id Tender ID
   * @return
   */
  public Optional<Tender> get(final String id) {
    return tenderRepository.findById(id);
  }

  /**
   * Update a particular tender
   *
   * @param id Tender ID
   * @param updatedTender The updated Tender objects
   * @param user User who is performing the action
   * @return
   */
  public Tender update(final String id, final TenderUpdate updatedTender, final User user) {
    Optional<Tender> optExistingTender = tenderRepository.findById(id);
    if (optExistingTender.isPresent()) {
      boolean relevantChange = false;
      Tender existingTender = optExistingTender.get();
      existingTender.setTitle(updatedTender.getTitle());
      existingTender.setSourceUrl(updatedTender.getSourceUrl());
      existingTender.setClient(updatedTender.getClient());
      existingTender.setSubmissionDate(updatedTender.getSubmissionDate());
      existingTender.setBindingDeadline(updatedTender.getBindingDeadline());
      existingTender.setContractDuration(updatedTender.getContractDuration());
      existingTender.setPublicationDate(updatedTender.getPublicationDate());
      existingTender.setQuestionDeadline(updatedTender.getQuestionDeadline());
      existingTender.setContractValue(updatedTender.getContractValue());
      existingTender.setMaximumBudget(updatedTender.getMaximumBudget());
      existingTender.setWinningCriteria(updatedTender.getWinningCriteria());
      existingTender.setWeightingPriceQuality(updatedTender.getWeightingPriceQuality());
      existingTender.setDeliveryLocation(updatedTender.getDeliveryLocation());
      existingTender.setWorkflowStatus(updatedTender.getWorkflowStatus());
      if (!Objects.equals(updatedTender.getDescription(), existingTender.getDescription())) {
        relevantChange = true;
        existingTender.setDescription(updatedTender.getDescription());
      }

      // update user evaluation fields
      existingTender.setComment(updatedTender.getComment());
      existingTender.setRating(updatedTender.getRating());
      existingTender.setIsFavorite(updatedTender.getIsFavorite());

      // update system-generated fields
      updateTenderMetadata(existingTender, user);
      existingTender = tenderRepository.save(existingTender);

      // TODO:
      // It most likely makes no sense that an event is published on every change, therefore
      // currently we only publish an event based on certain changes
      if (relevantChange) {
        eventPublisher.publishEvent(new TenderUpdated(existingTender.getId()));
      }
      return existingTender;
    }
    throw new ResourceNotFoundException("The requested resource could not be found.");
  }

  /**
   * Get all tenders that match certain criteria.
   *
   * @param pageable
   * @return List of matching Tenders
   */
  public Page<Tender> getAll(Pageable pageable) {
    return tenderRepository.findAll(pageable);
  }

    /**
     * Get all tenders that match certain criteria.
     *
     * @param pageable
     * @param workflowStatuses Workflow statuses to filter by
     * @param searchString Search string to filter by
     * @return List of matching Tenders
     */
    public Page<Tender> findAllTendersWithFilter(Pageable pageable, WorkflowStatus[] workflowStatuses, String searchString) {
        return tenderRepository.findAllByWorkflowStatusInAndTitleIsLikeIgnoreCaseOrWorkflowStatusInAndClientIsLikeIgnoreCase(workflowStatuses, searchString, workflowStatuses, searchString, pageable);
    }

  /**
   * Delete specific tender. Note: This will not throw an exception in case the specified tender
   * does not exist.
   *
   * @param id Id of the tender
   */
  public void delete(final String id) {
    Optional<Tender> optTender = tenderRepository.findById(id);
    if (optTender.isPresent()) {
      Tender tender = optTender.get();
      List<FileMetadata> filesMetadataList = tender.getFiles();
      if (filesMetadataList != null) {
        filesMetadataList.forEach(fileStorageService::delete);
      }
      extractedDocumentRepository.deleteByTenderId(id);
      tenderRepository.deleteById(id);
    } else {
      throw new ResourceNotFoundException("The requested resource could not be found.");
    }
  }

  /**
   * Delete file from Tender.
   *
   * @param tenderId Tender ID
   * @param fileId ID of the file to be deleted
   * @param user User who is performing the action
   */
  public void deleteFile(String tenderId, String fileId, User user) {
    Tender tender =
        tenderRepository
            .findById(tenderId)
            .orElseThrow(() -> new ResourceNotFoundException("Tender could not be found."));

    Optional<FileMetadata> optFile =
        tender.getFiles().stream()
            .filter(fileMetadata -> fileMetadata.getId().equals(fileId))
            .findFirst();
    FileMetadata fileMetadata =
        optFile.orElseThrow(() -> new ResourceNotFoundException("File could not be found."));

    boolean removed = tenderRepository.deleteFileAtomic(tenderId, fileId, user);
    if (!removed) {
      throw new ResourceNotFoundException("Failed to update tender to remove file.");
    }

    fileStorageService.delete(fileMetadata);

    extractedDocumentRepository
        .findByFileId(fileId)
        .ifPresent(doc -> extractedDocumentRepository.deleteById(doc.getId()));
  }

  /**
   * Add file to existing tender.
   *
   * @param tenderId Tender ID
   * @param file File to be added
   * @param user User who is performing the action
   */
  public FileMetadata addFile(String tenderId, MultipartFile file, User user) {
    if (file.isEmpty()) {
      throw new BadRequestException("File is empty");
    }
    FileMetadata newFileMetadata;
    try {
      newFileMetadata =
          fileStorageService.storeFile(
              tenderId, file.getOriginalFilename(), file.getInputStream(), file.getSize());
    } catch (Exception e) {
      throw new RuntimeException("Failed to store file in storage: " + e.getMessage(), e);
    }

    FileMetadata result = tenderRepository.addFileAtomic(tenderId, newFileMetadata, user);

    if (result == null) {
      fileStorageService.delete(newFileMetadata);
      throw new BadRequestException("File already exists or tender not found.");
    }

    Tender tender =
        tenderRepository
            .findById(tenderId)
            .orElseThrow(() -> new ResourceNotFoundException("Tender not found"));
    fileExtractionService.processFilesForExtraction(tender);

    return result;
  }

  /**
   * Download file that belongs to Tender.
   *
   * @param tenderId Tender ID
   * @param fileId File ID
   * @return Wrapper object that allows downloading the file
   */
  public FileDownloadRequest downloadFile(String tenderId, String fileId) {
    Optional<Tender> optTender = tenderRepository.findById(tenderId);
    if (optTender.isPresent()) {
      Tender tender = optTender.get();
      List<FileMetadata> existingFiles = tender.getFiles();
      if (existingFiles != null) {
        // Check if file with provided id exists
        Optional<FileMetadata> optFile =
            existingFiles.stream()
                .filter(fileMetadata -> fileMetadata.getId().equals(fileId))
                .findFirst();
        FileMetadata file =
            optFile.orElseThrow(() -> new ResourceNotFoundException("File could not be found."));
        FileDownloadRequest downloadRequest = new FileDownloadRequest();
        downloadRequest.setFileName(file.getFileName());
        downloadRequest.setFileSizeBytes(file.getFileSizeBytes());
        downloadRequest.setInputStream(fileStorageService.getFile(file));
        return downloadRequest;
      }
    }
    throw new ResourceNotFoundException("Tender could not be found.");
  }

  /** Builds the HTTP response for file download */
  public ResponseEntity<InputStreamResource> buildFileDownloadResponse(
      FileDownloadRequest downloadRequest) {
    String processedFileName = FileNameHelper.processFileName(downloadRequest.getFileName());
    HttpHeaders headers =
        createDownloadHeaders(processedFileName, downloadRequest.getFileSizeBytes());

    return ResponseEntity.ok()
        .headers(headers)
        .body(new InputStreamResource(downloadRequest.getInputStream()));
  }

  private HttpHeaders createDownloadHeaders(String fileName, long fileSize) {
    HttpHeaders headers = new HttpHeaders();
    headers.add(HttpHeaders.CONTENT_DISPOSITION, createContentDisposition(fileName));
    headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(fileSize));
    headers.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
    headers.add(HttpHeaders.PRAGMA, "no-cache");
    headers.add(HttpHeaders.EXPIRES, "0");
    return headers;
  }

  private String createContentDisposition(String fileName) {
    try {
      String asciiSafeName = FileNameHelper.getAsciiSafeName(fileName);
      String encodedName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replace("+", "%20");

      return String.format(
          "attachment; filename=\"%s\"; filename*=UTF-8''%s", asciiSafeName, encodedName);
    } catch (Exception e) {
      log.warn("Failed to create content disposition header for file: {}", fileName, e);
      return "attachment; filename=\"download\"";
    }
  }

  private void updateTenderMetadata(Tender tender, User user) {
    tender.setLastUpdatedTime(OffsetDateTime.now().truncatedTo(ChronoUnit.SECONDS).toString());
    tender.setLastUpdatedBy(user);
  }

  private void updateTenderWithExtractedFields(
      Tender tender, TenderExtractionResult extractionResult) {
    if (extractionResult.getClient() != null) {
      tender.setClient(extractionResult.getClient());
    }
    if (extractionResult.getSubmissionDate() != null) {
      tender.setSubmissionDate(extractionResult.getSubmissionDate());
    }
    if (extractionResult.getBindingDeadline() != null) {
      tender.setBindingDeadline(extractionResult.getBindingDeadline());
    }
    if (extractionResult.getContractDuration() != null) {
      tender.setContractDuration(extractionResult.getContractDuration());
    }
    if (extractionResult.getPublicationDate() != null) {
      tender.setPublicationDate(extractionResult.getPublicationDate());
    }
    if (extractionResult.getQuestionDeadline() != null) {
      tender.setQuestionDeadline(extractionResult.getQuestionDeadline());
    }
    if (extractionResult.getContractValue() != null) {
      tender.setContractValue(extractionResult.getContractValue());
    }
    if (extractionResult.getMaximumBudget() != null) {
      tender.setMaximumBudget(extractionResult.getMaximumBudget());
    }
    if (extractionResult.getWinningCriteria() != null) {
      tender.setWinningCriteria(extractionResult.getWinningCriteria());
    }
    if (extractionResult.getWeightingPriceQuality() != null) {
      tender.setWeightingPriceQuality(extractionResult.getWeightingPriceQuality());
    }
    if (extractionResult.getDeliveryLocation() != null) {
      tender.setDeliveryLocation(extractionResult.getDeliveryLocation());
    }
    if (extractionResult.getRating() != null) {
      tender.setRating(extractionResult.getRating());
    }
  }

  /** Returns true if all of the mandatory fields of the tender are non‐null and non‐empty. */
  private boolean allRequiredFieldsPresent(Tender tender) {
    return hasText(tender.getClient())
        && hasText(tender.getSubmissionDate())
        && hasText(tender.getBindingDeadline())
        && hasText(tender.getContractDuration())
        && hasText(tender.getPublicationDate())
        && hasText(tender.getQuestionDeadline())
        && hasText(tender.getWinningCriteria())
        && hasText(tender.getWeightingPriceQuality())
        && hasText(tender.getDeliveryLocation());
  }

  private static boolean hasText(String s) {
    return (s != null && !s.trim().isEmpty());
  }

  /**
   * Starts the analysis process for a tender, delegating to TenderAnalysisService.
   *
   * @param tenderId The ID of the tender to analyze
   * @return The analysis result
   * @throws ResourceNotFoundException if the tender is not found
   */
  public AnalysisResult startAnalysis(String tenderId) {
    log.info("Delegating analysis for tender: {}", tenderId);
    return tenderAnalysisService.analyzeTender(tenderId);
  }

  @EventListener(TenderCreated.class)
  @Async
  void handleEvent(TenderCreated event) {
    log.debug("Event handler received event: {}", event);
    startAnalysis(event.getTenderId());
  }

  @EventListener(TenderUpdated.class)
  @Async
  void handleEvent(TenderUpdated event) {
    log.debug("Event handler received event: {}", event);
    // startAnalysis(event.getTenderId());
  }

  //    @EventListener(TenderFilesUpdated.class)
  //    @Async
  //    void handleEvent(TenderFilesUpdated event) {
  // TODO think about whether this event should be fired if multiple files are uploaded in a row.
  // This can
  //  lead to unnecessary analysis processes
  //        logger.debug("Event handler received event: {}", event);
  //        startAnalysis(event.getTenderId());
  //    }
}
