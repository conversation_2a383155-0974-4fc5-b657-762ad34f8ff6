package com.gofore.aita.config;

import com.gofore.aita.tender.domain.models.Tender;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexDirection;
import org.springframework.data.mongodb.core.index.IndexOperations;

/**
 * Configuration class to ensure required MongoDB indexes are created.
 * This is particularly important for Azure Cosmos DB which requires indexes for sort operations.
 */
@Configuration
@Slf4j
public class MongoIndexConfig {

  private final MongoTemplate mongoTemplate;

  public MongoIndexConfig(MongoTemplate mongoTemplate) {
    this.mongoTemplate = mongoTemplate;
  }

  @PostConstruct
  public void createIndexes() {
    log.info("Creating MongoDB indexes for Tender collection");
    
    IndexOperations indexOps = mongoTemplate.indexOps(Tender.class);
    
    // Create index for workflowStatus field (required for sorting)
    Index workflowStatusIndex = new Index().on("workflowStatus", Sort.Direction.ASC);
    indexOps.ensureIndex(workflowStatusIndex);
    log.info("Created index for workflowStatus field");
//
//    // Create indexes for other sortable fields
//    Index contractValueIndex = new Index().on("contractValue", Sort.Direction.ASC);
//    indexOps.ensureIndex(contractValueIndex);
//    log.info("Created index for contractValue field");
//
//    Index creationTimeIndex = new Index().on("creationTime", Sort.Direction.ASC);
//    indexOps.ensureIndex(creationTimeIndex);
//    log.info("Created index for creationTime field");
//
//    Index lastUpdatedTimeIndex = new Index().on("lastUpdatedTime", Sort.Direction.ASC);
//    indexOps.ensureIndex(lastUpdatedTimeIndex);
//    log.info("Created index for lastUpdatedTime field");
    
    // Create compound index for filtering and sorting
    Index compoundIndex = new Index()
        .on("workflowStatus", Sort.Direction.ASC)
        .on("title", Sort.Direction.ASC)
        .on("client", Sort.Direction.ASC);
    indexOps.ensureIndex(compoundIndex);
    log.info("Created compound index for workflowStatus, title, and client fields");

    // Create text index for full-text search across multiple fields
    Index textIndex = new Index()
        .on("title", IndexDirection.TEXT)
        .on("client", IndexDirection.TEXT)
        .on("description", IndexDirection.TEXT)
        .on("deliveryLocation", IndexDirection.TEXT);
    indexOps.ensureIndex(textIndex);
    log.info("Created text index for full-text search across title, client, description, and deliveryLocation fields");
    
    log.info("MongoDB index creation completed");
  }
}
