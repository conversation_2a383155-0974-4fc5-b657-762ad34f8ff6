spring:
  data:
    mongodb:
      uri: ${AZURE_COSMOS_MONGO_CONNECTION_STRING}
      database: ${AZURE_COSMOS_MONGO_DATABASE}

#      host: localhost:27017
#      username: admin
#      password: password
#      database: aita-tender
#      authentication-database: admin

app:
  cors:
    origins: http://localhost:4200
  openai:
    endpoint: https://gofore-aita.openai.azure.com/
    deployment-name: oai-gpt-4o-mini-deployment

logging:
  level:
    com:
      gofore:
        aita: DEBUG
